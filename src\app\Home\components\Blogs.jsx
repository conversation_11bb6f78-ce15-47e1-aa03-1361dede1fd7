"use client";
import React, { useState, useEffect, useRef } from "react";
import { ArrowRight, ChevronLeft, ChevronRight } from "lucide-react";
import Image from 'next/image';

const MOBILE_BREAKPOINT = 767;

const blogs = [

            {
            image: "/images/Blog47.png",
            title: "Capture The Bug is Now CREST Accredited Penetration Testing Provider",
            date: "July 1, 2025",
            description: "In the world of cybersecurity, trust isn&apos;t given; it&apos;s earned. It&apos;s proven through rigorous processes, demonstrable expertise, and an unwavering commitment to quality. Today, we are thrilled to announce that Capture The Bug has earned that trust in a significant new way: we are now officially a CREST-accredited provider for penetration testing services.",
            readMoreLink: "/Blogs/Capture-The-Bug-is-Now-CREST-Accredited-Penetration-Testing-Provider",
          },
          {
            image: "/images/Blog46.png",
            title: "Don't Just Find Flaws, Fix Them: The Rise of the Purple Team",
            date: "July 30, 2025",
            description: "For years, cybersecurity has been a tale of two teams: Red Team attackers and Blue Team defenders. But what if they worked together? Discover how Purple Team Strategy transforms security testing from adversarial to collaborative, building truly resilient defenses through real-time feedback and continuous improvement.",
            readMoreLink: "/Blogs/Dont-Just-Find-Flaws-Fix-Them-The-Rise-of-the-Purple-Team",
          },
  {
    image: "/images/Blog45.png",
    title: "Penetration Testing for Fintech: Securing Innovation in the Digital Economy",
    date: "July 29, 2025",
    description: "The financial technology (fintech) sector is a cornerstone of the modern digital economy, driving innovation in payments, lending, investments, and more. However, this rapid pace of innovation, coupled with the highly sensitive nature of financial data, presents unique and complex cybersecurity challenges. Penetration testing for fintech is not merely a regulatory checkbox; it&apos;s a critical investment to safeguard innovation, maintain customer trust, and ensure resilience against a relentless landscape of cyber threats.",
    readMoreLink: "/Blogs/Penetration-Testing-for-Fintech-Securing-Innovation-in-the-Digital-Economy",
  },
  {
    image: "/images/Blog44.png",
    title: "Top 5 Penetration Testing Companies in the USA (2025 Edition)",
    date: "July 28, 2025",
    description: "At Capture The Bug, we're often asked how we compare to other penetration testing companies in the market. As industry leaders in innovative PTaaS technology and real-time vulnerability reporting, we believe transparency is key. So we've done the research for you-analyzing our competitors, their strengths, and what sets us apart in the rapidly evolving cybersecurity landscape.",
    readMoreLink: "/Blogs/Top-5-Penetration-Testing-Companies-in-the-USA-2025-Edition",
  },
  {
    image: "/images/Blog43.png",
    title: "From Zero-Day to Remediation: A Step-by-Step Incident Response Guide",
    date: "July 25, 2025",
    description: "Zero-day vulnerabilities represent the ultimate cybersecurity nightmare-unknown threats that bypass traditional defenses and leave organizations exposed to devastating attacks. Learn the critical steps for effective incident response from detection to remediation.",
    readMoreLink: "/Blogs/From-Zero-Day-to-Remediation-A-Step-by-Step-Incident-Response-Guide",
  },
  {
    image: "/images/Blog42.png",
    title: "Understanding Data Breaches: A Developer's Guide to Prevention",
    date: "July 24, 2025",
    description: "In today&apos;s digital landscape, data breaches have become one of the most pressing cybersecurity threats facing organizations worldwide. Learn essential security practices every developer needs to know to prevent data breaches through secure coding practices, proper authentication, and comprehensive security testing.",
    readMoreLink: "/Blogs/Understanding-Data-Breaches-A-Developers-Guide-to-Prevention",
  },
  {
    image: "/images/Blog41.png",
    title: "API Penetration Testing: Securing the Backbone of Modern Applications",
    date: "July 23, 2025",
    description: "In today&apos;s interconnected digital landscape, Application Programming Interfaces (APIs) have become the invisible foundation that powers everything from mobile apps to enterprise software integrations. However, this critical infrastructure often operates as the &quot;hidden attack surface&quot; that cybercriminals actively exploit. API penetration testing has emerged as an essential security practice that goes far beyond traditional web application testing, requiring specialized techniques to uncover vulnerabilities that could expose sensitive data and compromise entire business ecosystems.",
    readMoreLink: "/Blogs/API-Penetration-Testing-Securing-the-Backbone-of-Modern-Applications",
  },
  {
    image: "/images/Blog40.png",
    title: "Healthcare Security Testing: Protecting Patient Data in Digital Health Systems",
    date: "July 22, 2025",
    description: "The healthcare industry has undergone a massive digital transformation, with electronic health records (EHRs), telemedicine platforms, and connected medical devices becoming standard practice. However, this digital evolution has also created an expanded attack surface that cybercriminals actively exploit. Healthcare security testing is no longer optional-it&apos;s a critical requirement for protecting sensitive patient data, maintaining regulatory compliance, and ensuring the continuity of life-saving medical services.",
    readMoreLink: "/Blogs/Healthcare-Security-Testing-Protecting-Patient-Data-in-Digital-Health-Systems",
  },
  {
    image: "/images/Blog39.png",
    title: "How Ethical Hacking Bridges the Gap Between Attackers and Defenders in Modern Cybersecurity",
    date: "July 21, 2025",
    description: "In the chess match between cybercriminals and security professionals, there's a unique group of players who understand both sides of the board. Ethical hacking represents the art of thinking like an attacker while working to strengthen defenses, creating an essential bridge between offensive and defensive cybersecurity strategies.",
    readMoreLink: "/Blogs/How-Ethical-Hacking-Bridges-the-Gap-Between-Attackers-and-Defenders-in-Modern-Cybersecurity",
  },
  {
    image: "/images/Blog38.png",
    title: "From Seed to Secure: Why Startups Can't Afford to Skip Penetration Testing",
    date: "July 18, 2025",
    description: "In the fast-paced world of startups, security often takes a backseat to growth. But in 2025, this mindset is potentially fatal. Discover why startup security testing isn't a luxury-it's a foundational investment that protects IP, builds trust, and ensures survival.",
    readMoreLink: "/Blogs/From-Seed-to-Secure-Why-Startups-Cant-Afford-to-Skip-Penetration-Testing",
  },
  {
    image: "/images/Blog37.png",
    title: "Compliance-Driven Security: Why Regular Testing is Essential for Regulatory Success",
    date: "July 17, 2025",
    description: "In a world shaped by ever-tightening regulations, compliance is no longer just a checklist-it's a business necessity. Modern organizations must demonstrate rigorous cybersecurity practices to regulators, customers, and partners alike. Investing in frequent compliance-focused security testing, such as PCI DSS penetration testing, SOC 2 penetration testing, and HIPAA security testing, isn't just about avoiding fines-it's about building trust and resilience in a rapidly evolving threat and compliance landscape.",
    readMoreLink: "/Blogs/Compliance-Driven-Security-Why-Regular-Testing-is-Essential-for-Regulatory-Success",
  },
  {
    image: "/images/Blog36.png",
    title: "Network Penetration Testing: Securing Your Company Inside and Out",
    date: "July 16, 2025",
    description: "In today's interconnected world, businesses face mounting threats from cyber attackers who probe both the visible edges of networks and their hidden internal pathways. Network penetration testing is essential for detecting exploitable vulnerabilities before malicious actors do. Comprehensive testing encompasses both external penetration testing-your public-facing \"front doors\"-and internal penetration testing-the often-overlooked cracks within your digital walls.",
    readMoreLink: "/Blogs/Network-Penetration-Testing-Securing-Your-Company-Inside-and-Out",
  },
  {
    image: "/images/Blog35.png",
    title: "Red Team vs. Blue Team: What Every Business Should Know About Offensive and Defensive Security",
    date: "July 15, 2025",
    description: "Cyber threats are evolving at breakneck speed, and businesses can no longer afford to rely on a single line of defense. Modern security strategies hinge on understanding and leveraging the dynamic between Red Teams (offensive security) and Blue Teams (defensive security). Knowing how these teams operate, collaborate, and challenge each other is key to building a resilient security posture in 2025.",
    readMoreLink: "/Blogs/Red-Team-vs-Blue-Team-What-Every-Business-Should-Know-About-Offensive-and-Defensive-Security",
  },
  {
    image: "/images/Blog34.png",
    title: "Modern Frontend Security: Protecting Your Application Beyond XSS and CSRF in 2025",
    date: "July 14, 2025",
    description: "The frontend is no longer 'just the UI.' Modern web applications handle authentication, sensitive data, API calls, and business logic. Learn advanced security strategies to protect React, Angular, Vue applications from evolving threats.",
    readMoreLink: "/Blogs/Modern-Frontend-Security-Protecting-Your-Application-Beyond-XSS-and-CSRF-in-2025",
  },
  {
    image: "/images/Blog33.png",
    title: "Why SMEs and Healthcare Providers Need Cybersecurity Now More Than Ever",
    date: "July 11, 2025",
    description: "In today's hyper-connected world, both small and medium-sized enterprises (SMEs) and healthcare organizations face a relentless wave of cyber threats. Investing in cybersecurity services is no longer optional-it's essential for survival, reputation, and compliance.",
    readMoreLink: "/Blogs/Why-SMEs-and-Healthcare-Providers-Need-Cybersecurity-Now-More-Than-Ever",
  },
  {
    image: "/images/Blog32.png",
    title: "Cybersecurity Testing in Australia & New Zealand: Local Threats, Global Standards",
    date: "July 10, 2025",
    description: "As the digital landscape continues to evolve, businesses in Australia and New Zealand are facing a surge in cyber threats. Discover how robust cybersecurity testing addresses local threats while meeting global compliance standards.",
    readMoreLink: "/Blogs/Cybersecurity-Testing-in-Australia-New-Zealand-Local-Threats-Global-Standards",
  },
  {
    image: "/images/Blog31.png",
    title: "Why U.S. Businesses Need Penetration Testing Now More Than Ever",
    date: "July 09, 2025",
    description: "As cyber threats intensify and regulatory demands grow, penetration testing has become a critical pillar for American organizations seeking to protect sensitive data, ensure business continuity, and maintain compliance.",
    readMoreLink: "/Blogs/Why-U.S.-Businesses-Need-Penetration-Testing-Now-More-Than-Ever",
  },
  {
    image: "/images/blog30.png",
    title: "The Hidden Costs of Ignoring Regular Network Security Testing",
    date: "July 08, 2025",
    description: "Discover the true financial, reputational, and operational risks of skipping network security testing. Learn how proactive vulnerability assessment and penetration testing can save your business from costly breaches.",
    readMoreLink: "/Blogs/The-Hidden-Costs-of-Ignoring-Regular-Network-Security-Testing",
  },
  {
    image: "/images/Blog29.png",
    title: "Will Cybersecurity Vulnerabilities Ever Disappear? The Truth About the Evolving Threat Landscape",
    date: "July 07, 2025",
    description: "Despite decades of technological progress, will cybersecurity vulnerabilities ever truly disappear? Explore the persistent nature of security risks and how businesses can build resilience through effective vulnerability management.",
    readMoreLink: "/Blogs/Will-Cybersecurity-Vulnerabilities-Ever-Disappear",
  },
  {
    image: "/images/Blog28.png",
    title: "Penetration Testing vs Vulnerability Assessment: Which Security Approach Your Business Needs",
    date: "July 4, 2025",
    description: "Understand the key differences between penetration testing and vulnerability assessment, and discover which security approach best fits your business needs...",
    readMoreLink: "/Blogs/Penetration-Testing-vs-Vulnerability-Assessment-Which-Security-Approach-Your-Business-Needs",
  },
  {
    image: "/images/Blog27.png",
    title: "Web Application Security Testing: Beyond OWASP Top 10",
    date: "July 3, 2025",
    description: "While the OWASP Top 10 provides essential guidance, modern organizations face sophisticated threats that extend far beyond these foundational vulnerabilities. Discover how comprehensive security testing addresses business logic flaws and advanced persistent threats...",
    readMoreLink: "/Blogs/Web-Application-Security-Testing-Beyond-OWASP-Top-10",
  },
  {
    image: "/images/Blog26.png",
    title: "The Art of Effective Vulnerability Remediation and Retesting",
    date: " July 2, 2025",
    description: "Organizations spend millions on vulnerability assessment and penetration testing, yet 60% of successful cyberattacks exploit vulnerabilities that were previously identified but never properly remediated...",
    readMoreLink: "/Blogs/The-Art-of-Effective-Vulnerability-Remediation-and-Retesting",
  },
  {
    image: "/images/Blog25.png",
    title: "The Complete Guide to PTaaS: Modernizing Your Vulnerability Assessment Program",
    date: "July 1, 2025",
    description: "Traditional vulnerability assessment approaches are failing to keep pace with modern cybersecurity threats. PTaaS offers a revolutionary shift from periodic assessments to continuous security validation...",
    readMoreLink: "/Blogs/The-Complete-Guide-to-PTaaS-Modernizing-Your-Vulnerability-Assessment-Program",
  },
  {
    image: "/images/Blog24.png",
    title: "Manual vs Automated Penetration Testing: Why Human Expertise Is Important in 2025",
    date: "June 30, 2025",
    description: "While automation speeds up vulnerability detection, human expertise remains essential for comprehensive security. Learn why manual penetration testing is critical for identifying complex threats...",
    readMoreLink: "/Blogs/Manual-vs-Automated-Penetration-Testing",
  },
  {
    image: "/images/Blog23.png",
    title: "Prerequisites to Start a Vulnerability Assessment and Penetration Testing (VAPT)",
    date: "May 23, 2025",
    description: "Get VAPT-ready the smart way. This guide covers everything you need before starting a vulnerability assessment...",
    readMoreLink: "/Blogs/Prerequisites-to-Start-a-Vulnerability-Assessment-and-Penetration-Testing",
  },
  {
    image: "/images/Blog22.png",
    title: "What Is Vulnerability Assessment? A Step-by-Step Guide for AI-Era Cybersecurity",
    date: "May 23, 2025",
    description: "Stay ahead of cyber threats with smart, AI-powered Vulnerability Assessments. Our step-by-step guide breaks down...",
    readMoreLink: "/Blogs/What-Is-Vulnerability-Assessment",
  },
  {
    image: "/images/Blog21.jpg",
    title: "SaaS Security in 2025: What Modern Businesses Must Know About Pentesting & VAPT",
    date: "April 15, 2025",
    description: "Discover what SaaS security, pentesting, and VAPT mean for growing businesses in 2025. Learn how to protect your cloud applications...",
    readMoreLink: "/Blogs/SaaS-Security-in-2025-What-Modern-Businesses-Must-Know-About-Pentesting-&-VAPT",
  },
  {
    image: "/images/Blog20.png",
    title: "What is Penetration Testing as a Service(PTaaS): The Ultimate Guide for Fast-Growing Companies in ANZ",
    date: "April 11, 2025",
    description: "Discover how PTaaS enables agile security for ANZ startups. Continuous penetration testing....",
    readMoreLink: "/Blogs/What-is-Penetration-Testing-as-a-Service-PTaaS",
  },
  {
    image: "/images/Blog19.jpg",
    title: "5 Best Penetration Testing Companies in 2025 [Worldwide & ANZ]",
    date: "April 3, 2025",
    description: "In today's increasingly connected digital landscape, cybersecurity has become a critical concern for....",
    readMoreLink: "/Blogs/5-Best-Penetration-Testing-Companies-in-2025",
  },
  {
    image: "/images/Blog18.jpg",
    title: "Penetration Testing in New Zealand: Why Kiwi Businesses Need It Now More Than Ever",
    date: "April 1, 2025",
    description: "New Zealand's digital landscape is evolving fast - but so are the cyber threats. From Auckland to Invercargill...",
    readMoreLink: "/Blogs/Penetration-Testing-in-New-Zealand",
  },
  {
    image: "/images/Blog17.jpg",
    title: "PTaaS in ANZ: Continuous Penetration Testing for Australia and New Zealand",
    date: "March 19, 2025",
    description: "Cyber threats in ANZ are growing, making traditional testing ineffective. PTaaS offers continuous security with real-...",
    readMoreLink: "/Blogs/PTaaS-in-ANZ",
  },
  {
    image: "/images/Blog16.png",
    title: "Why Penetration Testing is Essential for ST4S",
    date: "Nov 15, 2024",
    description: "In an era where education technology is at the heart of learning, ensuring the safety and security of digital platforms is more....",
    readMoreLink: "/Blogs/Why-Penetration-Testing-is-Essential-for-Safer-Technologies-4-Schools",
  },
  {
    image: "/images/Blog15.png",
    title: "What is Penetration testing (Pentesting)?",
    date: "Sept 20, 2024",
    description: "In today's digital landscape, where cyber threats are growing in complexity, businesses can no longer rely on traditional....",
    readMoreLink: "/Blogs/What-Is-Penetration-Testing",
  },
  {
    image: "/images/Blog14.jpg",
    title: "Building Cyber Resilience with Continuous Pentesting",
    date: "Sept 12, 2024",
    description:
      "In today's rapidly evolving threat landscape, building cyber resilience is more critical than ever for New Zealand's tech companies....",
    readMoreLink: "/Blogs/Building-Cyber-Resilience-in-NZ",
  },
  {
    image: "/images/Blog13.jpg",
    title: "VAPT: An Affordable Solution for Businesses",
    date: "Sept 8, 2024",
    description:
      "In today's ever-evolving digital landscape, businesses face increasing cyber threats. Protecting sensitive data, maintaining customer....",
    readMoreLink: "/Blogs/Why-VAPT-Is-Essential-For-ANZ-Businesses",
  },
  {
    image: "/images/Blog9.jpg",
    title: "Agile Pentesting vs. Annual Pentesting",
    date: "Sept 6, 2024",
    description:
      "In today's rapidly evolving cyber landscape, organisations within the energy sector face increasing challenges. With critical infrastructure at stake, the need for....",
    readMoreLink:
      "/Blogs/Cybersecurity-Solution-for-New-Zealand-Energy-Companies",
  },
  {
    image: "/images/Blog10.jpg",
    title: "Why Airlines Need to Adopt Continuous Security Testing?",
    date: "Sept 4, 2024",
    description:
      "The aviation industry is a vital cog in global infrastructure, connecting millions of people, goods, and services every day. However....",
    readMoreLink: "/Blogs/A-Game-Changer-for-Airline-Cybersecurity",
  },
  {
    image: "/images/Blog11.jpg",
    title:
      "Why Fast Moving SaaS Companies in ANZ Should Adopt Agile Pentesting?",
    date: "Sept 2, 2024",
    description:
      "In the competitive and fast-paced world of SaaS (Software as a Service), where innovation, speed, and security are critical,....",
    readMoreLink:
      "/Blogs/Why-Fast-Moving-SaaS-Companies-in-ANZ-Should-Adopt-Agile-Pentesting",
  },
  {
    image: "/images/Blog12.jpg",
    title: "The Future of Healthcare Cybersecurity",
    date: "Aug 31, 2024",
    description:
      "As cyber threats targeting healthcare providers in New Zealand continue to rise, it's crucial to ask: Is your organization prepared to handle these,....",
    readMoreLink:
      "/Blogs/How-ANZ-Healthcare-Can-Stay-Ahead-of-Cyber-Threats-with-Continuous-Pentesting",
  },
  {
    image: "/images/cost-of-pentesting.jpg",
    title: "What's the Real Cost of Pentesting in AU & NZ?",
    date: "Aug 28, 2024",
    description:
      "The cost of a penetration test (pentest) can vary widely, depending on factors such as scope, complexity, and the level of expertise required...",
    readMoreLink: "/Blogs/Cost-Of-Pentesting",
  },
  {
    image: "/images/pentesting-challenges.jpg",
    title: "Tackling Pentesting Challenges in ANZ",
    date: "Aug 28, 2024",
    description:
      "As a leading PTaaS platform, Capture The Bug has identified several critical challenges, market gaps, and pain points...",
    readMoreLink: "/Blogs/Tackling-Pentesting-Challenges",
  },
  {
    image: "/images/Blog1.png",
    title: "What is Penetration Testing as a Service (PTaaS)?",
    date: "April 30, 2023",
    description:
      "In today's digital landscape, cybersecurity is a top priority for businesses of all sizes. Traditional methods of penetration testing....",
    readMoreLink: "/Blogs/What-is-Penetration-Testing-as-a-Service",
  },
  {
    image: "/images/Blog2.png",
    title:
      "The Evolution of Penetration Testing: From Traditional Methods to Agile PTaaS Solutions.",
    date: "April 30, 2023",
    description:
      "In the dynamic digital landscape, businesses must adapt swiftly to cybersecurity threats. Traditional penetration...",
    readMoreLink: "/Blogs/Evolution-of-pentesting",
  },
  {
    image: "/images/Blog4.png",
    title:
      "Integrating PTaaS into Your Cybersecurity Strategy: A Guide for CISOs",
    date: "April 30, 2023",
    description:
      "With cybersecurity threats rapidly evolving, Chief Information Security Officers (CISOs) must ensure their...",
    readMoreLink: "/Blogs/Integrating-ptaas",
  },
  {
    image: "/images/Blog5.png",
    title:
      "New Zealand became the latest nation to start mandating VDPs for government agencies",
    date: "April 30, 2023",
    description:
      "New Zealand's Government Communications Security Bureau (GCSB) has advised government agencies...",
    readMoreLink: "/Blogs/New-Zealand-became-the-latest-nation",
  },
  {
    image: "/images/Blog6.png",
    title: "Common Mistakes to Avoid in Penetration Testing",
    date: "April 30, 2023",
    description:
      "Penetration testing is a vital process for assessing the security posture of an organization's systems and networks. It involves simulating real-world attacks by...",
    readMoreLink: "/Blogs/Common-Mistakes",
  },
  {
    image: "/images/Blog3.png",
    title: "Community-Powered Pentesting: The Future of Cybersecurity",
    date: "April 30, 2023",
    description:
      "In the ever-evolving landscape of cybersecurity, traditional approaches to penetration testing are being challenged by innovative methodologies....",
    readMoreLink: "/Blogs/Community-powered-pentesting",
  },
];

const BlogCard = ({ image, title, date, description, readMoreLink }) => {
  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 h-full flex flex-col border border-gray-100 min-w-0 flex-shrink-0">
      <Image
        src={image}
        alt={title}
        className="w-full object-contain aspect-[3/2] rounded-t-2xl"
        width={360}
        height={240}
      />
      <div className="p-6 flex-1 flex flex-col">
        <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 leading-tight">
          {title}
        </h3>
        <p className="text-gray-600 text-sm mb-4 flex-1 line-clamp-3">
          {description}
        </p>
        <div className="flex items-center justify-between pt-2">
          <span className="text-xs text-gray-500">{date}</span>
          <a
            href={readMoreLink}
            className="group flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors"
          >
            Read more
            <ArrowRight className="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </a>
        </div>
      </div>
    </div>
  );
};

export default function BlogSection() {
  const [isMobile, setIsMobile] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const scrollContainerRef = useRef(null);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= MOBILE_BREAKPOINT);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', checkScrollButtons);
      return () => scrollContainer.removeEventListener('scroll', checkScrollButtons);
    }
  }, []);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      const cardWidth = scrollContainerRef.current.children[0]?.offsetWidth || 0;
      const gap = 32; // 8 * 4 = 32px (gap-8)
      const scrollDistance = isMobile ? cardWidth + gap : (cardWidth + gap) * 3;
      scrollContainerRef.current.scrollBy({
        left: -scrollDistance,
        behavior: 'smooth'
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      const cardWidth = scrollContainerRef.current.children[0]?.offsetWidth || 0;
      const gap = 32; // 8 * 4 = 32px (gap-8)
      const scrollDistance = isMobile ? cardWidth + gap : (cardWidth + gap) * 3;
      scrollContainerRef.current.scrollBy({
        left: scrollDistance,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="bg-gray-50 py-16">
      <div className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900">
            Read Industry Insights
          </h1>
        </div>
        
        <div className="relative group">
          {/* Navigation Buttons - Only show on desktop */}
          {!isMobile && (
            <>
              <button
                onClick={scrollLeft}
                disabled={!canScrollLeft}
                className={`absolute left-0 top-1/2 border-gray-200 border-4 -translate-y-1/2 z-10 ml-6 bg-white shadow-lg rounded-full p-3 transition-all duration-300 ${
                  canScrollLeft 
                    ? 'hover:bg-gray-50 hover:shadow-xl opacity-100' 
                    : 'opacity-50 cursor-not-allowed'
                } group-hover:opacity-100`}
                style={{ transform: 'translateY(-50%) translateX(-50%)' }}
              >
                <ChevronLeft className="w-5 h-5 font-bold text-gray-600" />
              </button>
              
              <button
                onClick={scrollRight}
                disabled={!canScrollRight}
                className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white border-gray-200 border-4 mr-5 shadow-lg rounded-full p-3 transition-all duration-300 ${
                  canScrollRight 
                    ? 'hover:bg-gray-50 hover:shadow-xl opacity-100' 
                    : 'opacity-50 cursor-not-allowed'
                } group-hover:opacity-100`}
                style={{ transform: 'translateY(-50%) translateX(50%)' }}
              >
                <ChevronRight className="w-5 h-5 font-bold text-gray-600 " />
              </button>
            </>
          )}

          {/* Scrollable Container */}
          <div className="md:px-4 lg:px-8 xl:px-16">
            <div 
              ref={scrollContainerRef}
              className="flex gap-8 overflow-x-auto scrollbar-hide scroll-smooth"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                WebkitScrollbar: { display: 'none' }
              }}
            >
              {blogs.map((blog, index) => (
                <div
                  key={blog.readMoreLink}
                  className={`${
                    isMobile 
                      ? 'w-80 flex-shrink-0' 
                      : 'w-full max-w-sm flex-shrink-0'
                  }`}
                  style={{
                    minWidth: isMobile ? '320px' : 'calc((100% - 64px) / 3)'
                  }}
                >
                  <BlogCard {...blog} />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}