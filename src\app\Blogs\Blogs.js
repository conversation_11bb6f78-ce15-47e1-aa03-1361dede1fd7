"use client";
import { motion } from 'framer-motion';
import React, { useState, useEffect, useMemo } from "react";
import Button from '../common/buttons/Button';
import { ArrowRight, ChevronRight, Clock, Calendar, Tag, Filter, Search, X, Sliders, ChevronDown } from "lucide-react";
import { format, subDays, parse, isAfter, isBefore, isEqual } from 'date-fns';
import Image from 'next/image';
import BreadcrumbNavigation from '../common/components/BreadcrumbNavigation';

const MOBILE_BREAKPOINT = 767;
const VISIBLE_BLOGS_COUNT = 6;

// Date range presets
const DATE_RANGES = [
  { label: 'Last 7 days', days: 7 },
  { label: 'Last 30 days', days: 30 },
  { label: 'Last 60 days', days: 60 },
  { label: 'Last 90 days', days: 90 },
  { label: 'Up to 360 days', days: 360 },
  { label: 'All time', days: null }
];

const blogs = [
  {
    image: "/images/Blog48.png",
    title: "AI in Business Is Booming - But So Are Attacks: Why Security Testing Is Non-Negotiable",
    date: "August 1, 2025",
    description: "78% of organizations now use AI, but 74% of cybersecurity professionals say AI-powered threats are a major challenge. Discover why AI security testing has become critical as businesses race to implement AI solutions while facing sophisticated AI-powered cyber threats.",
    readMoreLink: "/Blogs/AI-in-Business-Is-Booming-But-So-Are-Attacks-Why-Security-Testing-Is-Non-Negotiable",
    readTime: "10 min read",
    tags: ["AI Security", "Artificial Intelligence", "Cybersecurity", "Security Testing", "AI Threats", "Penetration Testing", "Data Protection", "Business Security", "AI Vulnerabilities", "PTaaS"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog47.png",
    title: "Capture The Bug is Now CREST Accredited Penetration Testing Provider",
    date: "July 1, 2025",
    description: "In the world of cybersecurity, trust isn't given; it's earned. It's proven through rigorous processes, demonstrable expertise, and an unwavering commitment to quality. Today, we are thrilled to announce that Capture The Bug has earned that trust in a significant new way: we are now officially a CREST-accredited provider for penetration testing services.",
    readMoreLink: "/Blogs/Capture-The-Bug-is-Now-CREST-Accredited-Penetration-Testing-Provider",
    readTime: "8 min read",
    tags: ["CREST Accreditation", "Penetration Testing", "Cybersecurity", "Quality Assurance", "Security Testing", "Compliance", "Professional Standards", "Vulnerability Assessment", "Security Partnership", "Industry Standards"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
          {
            image: "/images/Blog46.png",
            title: "Don't Just Find Flaws, Fix Them: The Rise of the Purple Team",
            date: "July 30, 2025",
            description: "For years, cybersecurity has been a tale of two teams: Red Team attackers and Blue Team defenders. But what if they worked together? Discover how Purple Team Strategy transforms security testing from adversarial to collaborative, building truly resilient defenses through real-time feedback and continuous improvement.",
            readMoreLink: "/Blogs/Dont-Just-Find-Flaws-Fix-Them-The-Rise-of-the-Purple-Team",
            readTime: "12 min read",
            tags: ["Purple Team", "Red Team", "Blue Team", "Cybersecurity", "Collaborative Security", "Security Testing", "Penetration Testing", "Defense Strategy", "Security Operations", "Threat Hunting"],
            featured: false,
            category: "Cybersecurity",
            author: "Capture The Bug Team",
          },
  {
    image: "/images/Blog45.png",
    title: "Penetration Testing for Fintech: Securing Innovation in the Digital Economy",
    date: "July 29, 2025",
    description: "The financial technology (fintech) sector is a cornerstone of the modern digital economy, driving innovation in payments, lending, investments, and more. However, this rapid pace of innovation, coupled with the highly sensitive nature of financial data, presents unique and complex cybersecurity challenges. Penetration testing for fintech is not merely a regulatory checkbox; it's a critical investment to safeguard innovation, maintain customer trust, and ensure resilience against a relentless landscape of cyber threats.",
    readMoreLink: "/Blogs/Penetration-Testing-for-Fintech-Securing-Innovation-in-the-Digital-Economy",
    readTime: "13 min read",
    tags: ["Fintech Security", "Penetration Testing", "Financial Services", "PCI DSS", "API Security", "Compliance", "Cybersecurity", "Digital Banking", "Payment Security", "Financial Technology"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog44.png",
    title: "Top 5 Penetration Testing Companies in the USA (2025 Edition)",
    date: "July 28, 2025",
    description: "At Capture The Bug, we're often asked how we compare to other penetration testing companies in the market. As industry leaders in innovative PTaaS technology and real-time vulnerability reporting, we believe transparency is key. So we've done the research for you-analyzing our competitors, their strengths, and what sets us apart in the rapidly evolving cybersecurity landscape.",
    readMoreLink: "/Blogs/Top-5-Penetration-Testing-Companies-in-the-USA-2025-Edition",
    readTime: "14 min read",
    tags: ["Penetration Testing", "Cybersecurity", "PTaaS", "Security Companies", "USA", "Compliance", "Security Testing", "Vulnerability Assessment", "Penetration Testing Companies", "Security Services"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog43.png",
    title: "From Zero-Day to Remediation: A Step-by-Step Incident Response Guide",
    date: "July 25, 2025",
    description: "Zero-day vulnerabilities represent the ultimate cybersecurity nightmare-unknown threats that bypass traditional defenses and leave organizations exposed to devastating attacks. Learn the critical steps for effective incident response from detection to remediation.",
    readMoreLink: "/Blogs/From-Zero-Day-to-Remediation-A-Step-by-Step-Incident-Response-Guide",
    readTime: "9 min read",
    tags: ["Zero-Day Vulnerabilities", "Incident Response", "Cybersecurity", "Penetration Testing", "Vulnerability Assessment", "Security Testing", "Threat Detection", "Remediation", "Network Security", "Forensic Analysis"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog42.png",
    title: "Understanding Data Breaches: A Developer's Guide to Prevention",
    date: "July 24, 2025",
    description: "In today&apos;s digital landscape, data breaches have become one of the most pressing cybersecurity threats facing organizations worldwide. Learn essential security practices every developer needs to know to prevent data breaches through secure coding practices, proper authentication, and comprehensive security testing.",
    readMoreLink: "/Blogs/Understanding-Data-Breaches-A-Developers-Guide-to-Prevention",
    readTime: "11 min read",
    tags: ["Data Breaches", "Developer Security", "Cybersecurity", "Vulnerability Prevention", "OWASP", "Security Best Practices", "Incident Response", "Data Protection", "Secure Coding", "Authentication"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog41.png",
    title: "API Penetration Testing: Securing the Backbone of Modern Applications",
    date: "July 23, 2025",
    description: "In today&apos;s interconnected digital landscape, Application Programming Interfaces (APIs) have become the invisible foundation that powers everything from mobile apps to enterprise software integrations. However, this critical infrastructure often operates as the &quot;hidden attack surface&quot; that cybercriminals actively exploit. API penetration testing has emerged as an essential security practice that goes far beyond traditional web application testing, requiring specialized techniques to uncover vulnerabilities that could expose sensitive data and compromise entire business ecosystems.",
    readMoreLink: "/Blogs/API-Penetration-Testing-Securing-the-Backbone-of-Modern-Applications",
    readTime: "12 min read",
    tags: ["API Security", "Penetration Testing", "API Testing", "OWASP", "Cybersecurity", "Vulnerability Assessment", "REST API", "GraphQL", "Mobile Security", "Healthcare Security", "Financial Services", "PCI DSS", "HIPAA"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog40.png",
    title: "Healthcare Security Testing: Protecting Patient Data in Digital Health Systems",
    date: "July 22, 2025",
    description: "The healthcare industry has undergone a massive digital transformation, with electronic health records (EHRs), telemedicine platforms, and connected medical devices becoming standard practice. However, this digital evolution has also created an expanded attack surface that cybercriminals actively exploit. Healthcare security testing is no longer optional-it&apos;s a critical requirement for protecting sensitive patient data, maintaining regulatory compliance, and ensuring the continuity of life-saving medical services.",
    readMoreLink: "/Blogs/Healthcare-Security-Testing-Protecting-Patient-Data-in-Digital-Health-Systems",
    readTime: "11 min read",
    tags: ["Healthcare Security", "HIPAA", "Medical Device Security", "Patient Data Protection", "Healthcare Compliance", "Penetration Testing", "Vulnerability Assessment", "Cybersecurity", "Healthcare IT", "Digital Health"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog39.png",
    title: "How Ethical Hacking Bridges the Gap Between Attackers and Defenders in Modern Cybersecurity",
    date: "July 21, 2025",
    description: "In the chess match between cybercriminals and security professionals, there's a unique group of players who understand both sides of the board. Ethical hacking represents the art of thinking like an attacker while working to strengthen defenses, creating an essential bridge between offensive and defensive cybersecurity strategies.",
    readMoreLink: "/Blogs/How-Ethical-Hacking-Bridges-the-Gap-Between-Attackers-and-Defenders-in-Modern-Cybersecurity",
    readTime: "9 min read",
    tags: ["Ethical Hacking", "Penetration Testing", "Cybersecurity", "Red Team", "Blue Team", "Security Testing", "White Hat Hacking", "Offensive Security", "Defensive Security", "PTaaS"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog38.png",
    title: "From Seed to Secure: Why Startups Can't Afford to Skip Penetration Testing",
    date: "July 18, 2025",
    description: "In the fast-paced world of startups, security often takes a backseat to growth. But in 2025, this mindset is potentially fatal. Discover why startup security testing isn't a luxury-it's a foundational investment that protects IP, builds trust, and ensures survival.",
    readMoreLink: "/Blogs/From-Seed-to-Secure-Why-Startups-Cant-Afford-to-Skip-Penetration-Testing",
    readTime: "10 min read",
    tags: ["Startup Security", "Penetration Testing", "Vulnerability Assessment", "Investor Due Diligence", "IP Protection", "Security Investment", "Early-stage Security", "Cybersecurity", "Risk Management", "Business Continuity"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog37.png",
    title: "Compliance-Driven Security: Why Regular Testing is Essential for Regulatory Success",
    date: "July 17, 2025",
    description: "In a world shaped by ever-tightening regulations, compliance is no longer just a checklist-it's a business necessity. Modern organizations must demonstrate rigorous cybersecurity practices to regulators, customers, and partners alike. Investing in frequent compliance-focused security testing, such as PCI DSS penetration testing, SOC 2 penetration testing, and HIPAA security testing, isn't just about avoiding fines-it's about building trust and resilience in a rapidly evolving threat and compliance landscape.",
    readMoreLink: "/Blogs/Compliance-Driven-Security-Why-Regular-Testing-is-Essential-for-Regulatory-Success",
    readTime: "11 min read",
    tags: ["Compliance", "PCI DSS", "SOC 2", "HIPAA", "GDPR", "ISO 27001", "Regulatory Testing", "Security Testing", "Penetration Testing", "Vulnerability Assessment"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog36.png",
    title: "Network Penetration Testing: Securing Your Company Inside and Out",
    date: "July 16, 2025",
    description: "In today's interconnected world, businesses face mounting threats from cyber attackers who probe both the visible edges of networks and their hidden internal pathways. Network penetration testing is essential for detecting exploitable vulnerabilities before malicious actors do. Comprehensive testing encompasses both external penetration testing-your public-facing \"front doors\"-and internal penetration testing-the often-overlooked cracks within your digital walls.",
    readMoreLink: "/Blogs/Network-Penetration-Testing-Securing-Your-Company-Inside-and-Out",
    readTime: "12 min read",
    tags: ["Network Penetration Testing", "External Testing", "Internal Testing", "Network Security", "Vulnerability Assessment", "Cybersecurity", "Penetration Testing", "Network Infrastructure", "Security Testing", "Compliance"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog35.png",
    title: "Red Team vs. Blue Team: What Every Business Should Know About Offensive and Defensive Security",
    date: "July 15, 2025",
    description: "Cyber threats are evolving at breakneck speed, and businesses can no longer afford to rely on a single line of defense. Modern security strategies hinge on understanding and leveraging the dynamic between Red Teams (offensive security) and Blue Teams (defensive security). Knowing how these teams operate, collaborate, and challenge each other is key to building a resilient security posture in 2025.",
    readMoreLink: "/Blogs/Red-Team-vs-Blue-Team-What-Every-Business-Should-Know-About-Offensive-and-Defensive-Security",
    readTime: "10 min read",
    tags: ["Red Team", "Blue Team", "Offensive Security", "Defensive Security", "Purple Teaming", "Cybersecurity Strategy", "Penetration Testing", "Incident Response", "Threat Hunting", "Security Assessment"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog34.png",
    title: "Modern Frontend Security: Protecting Your Application Beyond XSS and CSRF in 2025",
    date: "July 14, 2025",
    description: "The frontend is no longer 'just the UI.' Modern web applications handle authentication, sensitive data, API calls, and business logic. Learn advanced security strategies to protect React, Angular, Vue applications from evolving threats.",
    readMoreLink: "/Blogs/Modern-Frontend-Security-Protecting-Your-Application-Beyond-XSS-and-CSRF-in-2025",
    readTime: "12 min read",
    tags: ["Frontend Security", "React Security", "Angular Security", "Vue Security", "XSS Prevention", "CSRF Protection", "Web Application Security", "JavaScript Security"],
    featured: false,
    category: "Web Security",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog33.png",
    title: "Why SMEs and Healthcare Providers Need Cybersecurity Now More Than Ever",
    date: "July 11, 2025",
    description: "In today's hyper-connected world, both small and medium-sized enterprises (SMEs) and healthcare organizations face a relentless wave of cyber threats. Investing in cybersecurity services is no longer optional-it's essential for survival, reputation, and compliance.",
    readMoreLink: "/Blogs/Why-SMEs-and-Healthcare-Providers-Need-Cybersecurity-Now-More-Than-Ever",
    readTime: "11 min read",
    tags: ["SME Cybersecurity", "Healthcare Security", "Vulnerability Assessment", "Penetration Testing", "HIPAA", "Privacy Act", "Business Continuity"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog32.png",
    title: "Cybersecurity Testing in Australia & New Zealand: Local Threats, Global Standards",
    date: "July 10, 2025",
    description: "As the digital landscape continues to evolve, businesses in Australia and New Zealand are facing a surge in cyber threats. Discover how robust cybersecurity testing addresses local threats while meeting global compliance standards.",
    readMoreLink: "/Blogs/Cybersecurity-Testing-in-Australia-New-Zealand-Local-Threats-Global-Standards",
    readTime: "10 min read",
    tags: ["ANZ Cybersecurity", "Penetration Testing", "Compliance", "ACSC", "CERT NZ", "Essential Eight"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog31.png",
    title: "Why U.S. Businesses Need Penetration Testing Now More Than Ever",
    date: "July 09, 2025",
    description: "As cyber threats intensify and regulatory demands grow, penetration testing has become a critical pillar for American organizations seeking to protect sensitive data, ensure business continuity, and maintain compliance.",
    readMoreLink: "/Blogs/Why-U.S.-Businesses-Need-Penetration-Testing-Now-More-Than-Ever",
    readTime: "9 min read",
    tags: ["Penetration Testing", "U.S. Cybersecurity", "Compliance", "SOC 2", "PCI DSS", "HIPAA"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/blog30.png",
    title: "The Hidden Costs of Ignoring Regular Network Security Testing",
    date: "July 08, 2025",
    description: "Discover the true financial, reputational, and operational risks of skipping network security testing. Learn how proactive vulnerability assessment and penetration testing can save your business from costly breaches.",
    readMoreLink: "/Blogs/The-Hidden-Costs-of-Ignoring-Regular-Network-Security-Testing",
    readTime: "8 min read",
    tags: ["Network Security", "Vulnerability Assessment", "Penetration Testing", "Cybersecurity", "Risk Management"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog29.png",
    title: "Will Cybersecurity Vulnerabilities Ever Disappear? The Truth About the Evolving Threat Landscape",
    date: "July 07, 2025",
    description: "Despite decades of technological progress, will cybersecurity vulnerabilities ever truly disappear? Explore the persistent nature of security risks and how businesses can build resilience through effective vulnerability management.",
    readMoreLink: "/Blogs/Will-Cybersecurity-Vulnerabilities-Ever-Disappear",
    readTime: "9 min read",
    tags: ["Cybersecurity", "Vulnerability Management", "Risk Management"],
    featured: false,
    category: "Cybersecurity",
    author: "Capture The Bug Team",
  },
  {
    image: "/images/Blog28.png",
    title:
      "Penetration Testing vs Vulnerability Assessment: Which Security Approach Your Business Needs",
    date: "July 4, 2025",
    description:
      "Understand the key differences between penetration testing and vulnerability assessment, and discover which security approach best fits your business needs...",
    readMoreLink:
      "/Blogs/Penetration-Testing-vs-Vulnerability-Assessment-Which-Security-Approach-Your-Business-Needs",
    readTime: "12 min read",
    tags: ["Penetration Testing", "Vulnerability Assessment", "Security"],
    featured: false,
  },
  {
    image: "/images/Blog27.png",
    title:
      "Web Application Security Testing: Beyond OWASP Top 10",
    date: "July 3, 2025",
    description:
      "While the OWASP Top 10 provides essential guidance, modern organizations face sophisticated threats that extend far beyond these foundational vulnerabilities. Discover how comprehensive security testing addresses business logic flaws and advanced persistent threats...",
    readMoreLink:
      "/Blogs/Web-Application-Security-Testing-Beyond-OWASP-Top-10", 
    readTime: "13 min read",  
    tags: ["Web Security", "OWASP", "Penetration Testing"],  
    featured: false, 
  },
  {
    image: "/images/Blog26.png",
    title:
      "The Art of Effective Vulnerability Remediation and Retesting",
    date: " July 2, 2025",
    description:
      "Organizations spend millions on vulnerability assessment and penetration testing, yet 60% of successful cyberattacks exploit vulnerabilities that were previously identified but never properly remediated...",
    readMoreLink:
      "/Blogs/The-Art-of-Effective-Vulnerability-Remediation-and-Retesting", 
    readTime: "11 min read",  
    tags: ["Remediation", "Retesting", "Security Validation"],  
    featured: false, 
  },
  {
    image: "/images/Blog25.png",
    title:
      "The Complete Guide to PTaaS: Modernizing Your Vulnerability Assessment Program",
    date: "July 1, 2025",
    description:
      "Traditional vulnerability assessment approaches are failing to keep pace with modern cybersecurity threats. PTaaS offers a revolutionary shift from periodic assessments to continuous security validation...",
    readMoreLink:
      "/Blogs/The-Complete-Guide-to-PTaaS-Modernizing-Your-Vulnerability-Assessment-Program", 
    readTime: "12 min read",  
    tags: ["PTaaS", "Vulnerability Assessment", "Cybersecurity"],  
    featured: false, 
  },
  {
    image: "/images/Blog24.png",
    title:
      "Manual vs Automated Penetration Testing: Why Human Expertise Is Important in 2025",
    date: "June 30, 2025",
    description:
      "While automation speeds up vulnerability detection, human expertise remains essential for comprehensive security. Learn why manual penetration testing is critical for identifying complex threats...",
    readMoreLink:
      "/Blogs/Manual-vs-Automated-Penetration-Testing", 
    readTime: "10 min read",  
    tags: ["Manual Testing", "Automation", "Human Expertise"],  
    featured: false, 
  },
  {
    image: "/images/Blog23.png",
    title:
      "Prerequisites to Start a Vulnerability Assessment and Penetration Testing (VAPT)",
    date: "May 23, 2025",
    description:
      "Get VAPT-ready the smart way. This guide covers everything you need before starting a vulnerability assessment...",
    readMoreLink:
      "/Blogs/Prerequisites-to-Start-a-Vulnerability-Assessment-and-Penetration-Testing", 
    readTime: "8 min read",  
    tags: ["VAPT", "Cybersecurity", "Assessment"],  
    featured: true, 
  },
  {
    image: "/images/Blog22.png",
    title:
      "What Is Vulnerability Assessment? A Step-by-Step Guide for AI-Era Cybersecurity",
    date: "May 23, 2025",
    description:
      "Stay ahead of cyber threats with smart, AI-powered Vulnerability Assessments. Our step-by-step guide breaks down...",
    readMoreLink: "/Blogs/What-Is-Vulnerability-Assessment", 
    readTime: "12 min read", 
    tags: ["AI", "Vulnerability", "Assessment"], 
    featured: false, 
  },
  {
    image: "/images/Blog21.jpg",
    title:
      "SaaS Security in 2025: What Modern Businesses Must Know About Pentesting & VAPT",
    date: "April 15, 2025",
    description:
      "Discover what SaaS security, pentesting, and VAPT mean for growing businesses in 2025. Learn how to protect your cloud applications...",
    readMoreLink:
      "/Blogs/SaaS-Security-in-2025-What-Modern-Businesses-Must-Know-About-Pentesting-&-VAPT", 
    readTime: "10 min read", 
    tags: ["SaaS", "Cloud Security", "Pentesting"], 
    featured: false, 
  },
  {
    image: "/images/Blog20.png",
    title:
      "What is Penetration Testing as a Service(PTaaS): The Ultimate Guide for Fast-Growing Companies in ANZ",
    date: "April 11, 2025",
    description:
      "Discover how PTaaS enables agile security for ANZ startups. Continuous penetration testing....",
    readMoreLink: "/Blogs/What-is-Penetration-Testing-as-a-Service-PTaaS", 
    readTime: "15 min read",  
    tags: ["PTaaS", "ANZ", "Startups"],  
    featured: false,  
  },
  {
    image: "/images/Blog19.jpg",
    title: "5 Best Penetration Testing Companies in 2025 [Worldwide & ANZ]",
    date: "April 3, 2025",
    description:
      "In today's increasingly connected digital landscape, cybersecurity has become a critical concern for....",
    readMoreLink: "/Blogs/5-Best-Penetration-Testing-Companies-in-2025", 
    readTime: "12 min read", 
    tags: ["Companies", "Review", "ANZ"],  
    featured: false,  
  },
  {
    image: "/images/Blog18.jpg",
    title:
      "Penetration Testing in New Zealand: Why Kiwi Businesses Need It Now More Than Ever",
    date: "April 1, 2025",
    description:
      "New Zealand&apos;s digital landscape is evolving fast - but so are the cyber threats. From Auckland to Invercargill...",
    readMoreLink: "/Blogs/Penetration-Testing-in-New-Zealand", 
    readTime: "9 min read",  
    tags: ["New Zealand", "Business", "Threats"], 
    featured: false, 
  },
  {
    image: "/images/Blog17.jpg",
    title:
      "PTaaS in ANZ: Continuous Penetration Testing for Australia and New Zealand",
    date: "March 19, 2025",
    description:
      "Cyber threats in ANZ are growing, making traditional testing ineffective. PTaaS offers continuous security with real-...",
    readMoreLink: "/Blogs/PTaaS-in-ANZ", 
    readTime: "11 min read",  
    tags: ["ANZ", "Continuous", "Security"], 
    featured: false,  
  },
  {
    image: "/images/Blog16.png",
    title: "Why Penetration Testing is Essential for ST4S",
    date: "Nov 15, 2024",
    description:
      "In an era where education technology is at the heart of learning, ensuring the safety and security of digital platforms is more....",
    readMoreLink:
      "/Blogs/Why-Penetration-Testing-is-Essential-for-Safer-Technologies-4-Schools", 
    readTime: "7 min read",  
    tags: ["Education", "Safety", "Schools"],  
    featured: false,  
  },
  {
    image: "/images/Blog15.png",
    title: "What is Penetration testing (Pentesting)?",
    date: "Sept 20, 2024",
    description:
      "In today's digital landscape, where cyber threats are growing in complexity, businesses can no longer rely on traditional....",
    readMoreLink: "/Blogs/What-Is-Penetration-Testing", 
    readTime: "8 min read", 
    tags: ["Basics", "Introduction", "Pentesting"], 
    featured: false,  
  },
  {
    image: "/images/Blog14.jpg",
    title: "Building Cyber Resilience with Continuous Pentesting",
    date: "Sept 12, 2024",
    description:
      "In today's rapidly evolving threat landscape, building cyber resilience is more critical than ever for New Zealand's tech companies....",
    readMoreLink: "/Blogs/Building-Cyber-Resilience-in-NZ", 
    readTime: "13 min read", 
    tags: ["Resilience", "Continuous", "New Zealand"], 
    featured: false, 
  },
  {
    image: "/images/Blog13.jpg",
    title: "VAPT: An Affordable Solution for Businesses",
    date: "Sept 8, 2024",
    description:
      "In today's ever-evolving digital landscape, businesses face increasing cyber threats. Protecting sensitive data, maintaining customer....",
    readMoreLink: "/Blogs/Why-VAPT-Is-Essential-For-ANZ-Businesses",
    readTime: "9 min read", 
    tags: ["VAPT", "Business", "Cost-effective"],  
    featured: false,
  },
  {
    image: "/images/Blog9.jpg",
    title: "Agile Pentesting vs. Annual Pentesting",
    date: "Sept 6, 2024",
    description:
      "In today's rapidly evolving cyber landscape, organisations within the energy sector face increasing challenges. With critical infrastructure at stake, the need for....",
    readMoreLink:
      "/Blogs/Cybersecurity-Solution-for-New-Zealand-Energy-Companies", 
    readTime: "10 min read", 
    tags: ["Agile", "Pentesting", "Comparison"],  
    featured: false, 
  },
  {
    image: "/images/Blog10.jpg",
    title: "Why Airlines Need to Adopt Continuous Security Testing?",
    date: "Sept 4, 2024",
    description:
      "The aviation industry is a vital cog in global infrastructure, connecting millions of people, goods, and services every day. However....",
    readMoreLink: "/Blogs/A-Game-Changer-for-Airline-Cybersecurity", 
    readTime: "8 min read",  
    tags: ["Aviation", "Continuous Testing", "Security"],  
    featured: true,  
  },
  {
    image: "/images/Blog11.jpg",
    title:
      "Why Fast Moving SaaS Companies in ANZ Should Adopt Agile Pentesting?",
    date: "Sept 2, 2024",
    description:
      "In the competitive and fast-paced world of SaaS (Software as a Service), where innovation, speed, and security are critical,....",
    readMoreLink:
      "/Blogs/Why-Fast-Moving-SaaS-Companies-in-ANZ-Should-Adopt-Agile-Pentesting", 
    readTime: "11 min read",  
    tags: ["SaaS", "ANZ", "Agile Pentesting"],  
    featured: false, 
  },
  {
    image: "/images/Blog12.jpg",
    title: "The Future of Healthcare Cybersecurity",
    date: "Aug 31, 2024",
    description:
      "As cyber threats targeting healthcare providers in New Zealand continue to rise, it's crucial to ask: Is your organization prepared to handle these,....",
    readMoreLink:
      "/Blogs/How-ANZ-Healthcare-Can-Stay-Ahead-of-Cyber-Threats-with-Continuous-Pentesting", 
    readTime: "10 min read",  
    tags: ["Healthcare", "ANZ", "Cybersecurity"],  
    featured: false, 
  },
  {
    image: "/images/cost-of-pentesting.jpg",
    title: "What's the Real Cost of Pentesting in AU & NZ?",
    date: "Aug 28, 2024",
    description:
      "The cost of a penetration test (pentest) can vary widely, depending on factors such as scope, complexity, and the level of expertise required...",
    readMoreLink: "/Blogs/Cost-Of-Pentesting", 
    readTime: "7 min read",  
    tags: ["Cost", "Pentesting", "ANZ"],  
    featured: true,
  },
  {
    image: "/images/pentesting-challenges.jpg",
    title: "Tackling Pentesting Challenges in ANZ",
    date: "Aug 28, 2024",
    description:
      "As a leading PTaaS platform, Capture The Bug has identified several critical challenges, market gaps, and pain points...",
    readMoreLink: "/Blogs/Tackling-Pentesting-Challenges",
    category: "PTaaS",  
    readTime: "6 min read",  
    tags: ["Challenges", "Pentesting", "ANZ"], 
    featured: false,
  },
  {
    image: "/images/Blog1.png",
    title: "What is Penetration Testing as a Service (PTaaS)?",
    date: "April 30, 2023",
    description:
      "In today's digital landscape, cybersecurity is a top priority for businesses of all sizes. Traditional methods of penetration testing....",
    readMoreLink: "/Blogs/What-is-Penetration-Testing-as-a-Service",
    category: "PTaaS",  
    readTime: "9 min read",  
    tags: ["PTaaS", "Introduction", "Basics"],  
    featured: false, 
  },
  {
    image: "/images/Blog2.png",
    title:
      "The Evolution of Penetration Testing: From Traditional Methods to Agile PTaaS Solutions.",
    date: "April 30, 2023",
    description:
      "In the dynamic digital landscape, businesses must adapt swiftly to cybersecurity threats. Traditional penetration...",
    readMoreLink: "/Blogs/Evolution-of-pentesting",
    category: "Penetration Testing", 
    readTime: "10 min read", 
    tags: ["Evolution", "Agile", "PTaaS"],  
    featured: false,
  },
  {
    image: "/images/Blog4.png",
    title:
      "Integrating PTaaS into Your Cybersecurity Strategy: A Guide for CISOs",
    date: "April 30, 2023",
    description:
      "With cybersecurity threats rapidly evolving, Chief Information Security Officers (CISOs) must ensure their...",
    readMoreLink: "/Blogs/Integrating-ptaas", 
    readTime: "12 min read",  
    tags: ["CISOs", "Strategy", "Integration"],  
    featured: false,
  },
  {
    image: "/images/Blog5.png",
    title:
      "New Zealand became the latest nation to start mandating VDPs for government agencies",
    date: "April 30, 2023",
    description:
      "New Zealand's Government Communications Security Bureau (GCSB) has advised government agencies...",
    readMoreLink: "/Blogs/New-Zealand-became-the-latest-nation",
    category: "Compliance",  
    readTime: "7 min read",  
    tags: ["New Zealand", "VDP", "Government"], 
    featured: false,
  },
  {
    image: "/images/Blog6.png",
    title: "Common Mistakes to Avoid in Penetration Testing",
    date: "April 30, 2023",
    description:
      "Penetration testing is a vital process for assessing the security posture of an organization's systems and networks. It involves simulating real-world attacks by...",
    readMoreLink: "/Blogs/Common-Mistakes", 
    readTime: "8 min read",  
    tags: ["Mistakes", "Pentesting", "Tips"],  
    featured: false,
  },
  {
    image: "/images/Blog3.png",
    title: "Community-Powered Pentesting: The Future of Cybersecurity",
    date: "April 30, 2023",
    description:
      "In the ever-evolving landscape of cybersecurity, traditional approaches to penetration testing are being challenged by innovative methodologies....",
    readMoreLink: "/Blogs/Community-powered-pentesting",
    readTime: "10 min read",
    tags: ["Community", "Pentesting", "Innovation"],
    featured: false,
  },
  
];

// Custom slider component for date range
const DateRangeSlider = ({ value, onChange, min, max }) => {
  const percentage = ((value - min) / (max - min)) * 100;
  
  return (
    <div className="relative w-full mt-4 mb-6">
      <div className="flex justify-between mb-2">
        <span className="text-sm font-medium text-gray-700">{min} days</span>
        <span className="text-sm font-medium text-blue-600">{value} days</span>
        <span className="text-sm font-medium text-gray-700">{max} days</span>
      </div>
      <div className="relative h-2">
        <div className="absolute inset-0 bg-gray-200 rounded-full"></div>
        <div 
          className="absolute inset-y-0 left-0 bg-blue-500 rounded-full"
          style={{ width: `${percentage}%` }}
        ></div>
        <div 
          className="absolute top-1/2 w-5 h-5 bg-white border-2 border-blue-500 rounded-full shadow-md transform -translate-x-1/2 -translate-y-1/2 transition-transform duration-150 hover:scale-110 cursor-pointer"
          style={{ left: `${percentage}%` }}
        ></div>
        <input
          type="range"
          min={min}
          max={max}
          value={value}
          onChange={e => onChange(parseInt(e.target.value))}
          className="absolute inset-0 w-full h-6 opacity-0 cursor-pointer z-10"
          style={{ height: '20px', top: '-10px' }}
        />
      </div>
    </div>
  );
};

// Custom tag pill component
const TagPill = ({ tag, selected, onClick }) => {
  return (
    <motion.button
      whileTap={{ scale: 0.95 }}
      onClick={() => onClick(tag)}
      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-1.5 ${
        selected 
          ? 'bg-blue-100 text-blue-700 border border-blue-200' 
          : 'bg-gray-100 text-gray-600 border border-transparent hover:bg-gray-200'
      }`}
    >
      {tag}
      {selected && (
        <X className="w-3 h-3" />
      )}
    </motion.button>
  );
};

const FeaturedBlogCard = ({ blog }) => {
  return (
    <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-3xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-500 border border-blue-100 group">
      <div className="relative">
        <Image
          src={blog.image}
          alt={blog.title}
          className="w-full h-48 sm:h-56 lg:h-60 object-contain group-hover:scale-105 transition-transform duration-500"
          width={600}
          height={240}
        />
        <div className="absolute top-3 sm:top-4 left-3 sm:left-4">
          <span className="bg-blue-600 text-white px-2 sm:px-3 py-1 rounded-full text-xs font-medium shadow-lg">
            Featured
          </span>
        </div>
        <div className="absolute top-3 sm:top-4 right-3 sm:right-4">
          <span className="bg-white/90 backdrop-blur-sm text-gray-700 px-2 sm:px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1 shadow-sm">
            <Clock className="w-3 h-3" />
            {blog.readTime}
          </span>
        </div>
      </div>
      <div className="p-4 sm:p-6 lg:p-8"> 
        <span className="text-gray-500 text-sm flex items-center gap-1 mb-3 sm:mb-4">
          <Calendar className="w-3 h-3" />
          {blog.date}
        </span> 
        <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4 line-clamp-2 leading-tight group-hover:text-blue-700 transition-colors">
          {blog.title}
        </h3>
        <p className="text-gray-600 mb-4 sm:mb-5 line-clamp-3 text-sm sm:text-xs leading-relaxed">
          {blog.description}
        </p>
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
          <div className="flex gap-2 flex-wrap">
            {blog.tags?.slice(0, 2).map((tag) => (
              <span key={tag} className="bg-gray-100 text-gray-600 px-2 py-1 rounded-md text-xs">
                {tag}
              </span>
            ))}
          </div>
          <a
            href={blog.readMoreLink}
            className="group/link text-sm flex items-center text-blue-600 hover:text-blue-700 font-semibold transition-colors shrink-0"
          >
            Read Article
            <ArrowRight className="ml-2 w-4 sm:w-5 h-4 sm:h-5 group-hover/link:translate-x-1 transition-transform" />
          </a>
        </div>
      </div>
    </div>
  );
};

const BlogCard = ({ blog }) => {
  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 h-full flex flex-col border border-gray-100 group">
      <div className="relative">
        <Image
          src={blog.image}
          alt={blog.title}
          className="w-full h-48 sm:h-56 lg:h-60 object-contain group-hover:scale-105 transition-transform duration-300"
          width={600}
          height={240}
        />
        <div className="absolute top-3 right-3">
          <span className="bg-white/90 backdrop-blur-sm text-gray-700 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 shadow-sm">
            <Clock className="w-3 h-3" />
            {blog.readTime}
          </span>
        </div>
      </div>
      <div className="p-4 sm:p-6 flex-1 flex flex-col">
        <div className="flex items-center gap-2 mb-3"> 
          <span className="text-gray-500 text-xs flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            {blog.date}
          </span>
        </div>
        <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 line-clamp-2 leading-tight group-hover:text-blue-700 transition-colors">
          {blog.title}
        </h3>
        <p className="text-gray-600 text-sm sm:text-xs flex-1 line-clamp-3 leading-relaxed mb-4">
          {blog.description}
        </p>
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0 mt-auto">
          <div className="flex gap-1 flex-wrap">
            {blog.tags?.slice(0, 2).map((tag) => (
              <span key={tag} className="bg-gray-50 text-gray-600 px-2 py-1 rounded text-xs">
                {tag}
              </span>
            ))}
          </div>
          <a
            href={blog.readMoreLink}
            className="group/link flex text-sm items-center text-blue-800 hover:text-blue-700 font-medium transition-colors shrink-0"
          >
            Read more
            <ArrowRight className="ml-1 w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
          </a>
        </div>
      </div>
    </div>
  );
};

// Filter panel component
const FilterPanel = ({ 
  searchTerm, 
  setSearchTerm, 
  dateRange, 
  setDateRange, 
  selectedTags, 
  setSelectedTags,
  customDateRange,
  setCustomDateRange,
  isFilterOpen,
  setIsFilterOpen,
  isMobile
}) => {
  
  // Get unique tags from all blogs
  const uniqueTags = useMemo(() => {
    const tagsSet = new Set();
    blogs.forEach(blog => {
      if (blog.tags) {
        blog.tags.forEach(tag => tagsSet.add(tag));
      }
    });
    return Array.from(tagsSet).sort();
  }, []);
  
  const handleTagToggle = (tag) => {
    setSelectedTags(prev => {
      if (prev.includes(tag)) {
        return prev.filter(t => t !== tag);
      } else {
        return [...prev, tag];
      }
    });
  };
  
  const clearFilters = () => {
    setSearchTerm('');
    setDateRange('All time');
    setSelectedTags([]);
    setCustomDateRange(360);
  };

  // Count active filters
  const activeFiltersCount = 
    (searchTerm ? 1 : 0) + 
    (dateRange !== 'All time' ? 1 : 0) + 
    selectedTags.length;
  
  return (
    <div className="mb-8 sm:mb-12">
      {/* Search and filter toggle bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-center mb-6">
        <div className="relative flex-1 w-full">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search articles by title, description or tag..."
            className="w-full px-4 py-3 pl-12 rounded-xl border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-200"
          />
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          {searchTerm && (
            <button 
              onClick={() => setSearchTerm('')}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
        
        <button 
          onClick={() => setIsFilterOpen(!isFilterOpen)}
          className="flex-shrink-0 flex items-center gap-2 px-5 py-3 bg-white border border-gray-200 hover:border-blue-300 rounded-xl text-gray-700 font-medium transition-all duration-200"
        >
          <Filter className={`w-5 h-5 ${isFilterOpen ? 'text-blue-500' : 'text-gray-500'}`} />
          <span>Filters</span>
          {activeFiltersCount > 0 && (
            <span className="flex items-center justify-center w-5 h-5 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full">
              {activeFiltersCount}
            </span>
          )}
          <ChevronDown className={`w-4 h-4 ml-1 transition-transform duration-300 ${isFilterOpen ? 'rotate-180 text-blue-500' : 'text-gray-500'}`} />
        </button>
      </div>
      
      {/* Active filters display */}
      {activeFiltersCount > 0 && (
        <motion.div 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-wrap items-center gap-2 mb-4"
        >
          <span className="text-sm text-gray-500">Active filters:</span>
          
          {searchTerm && (
            <span className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm flex items-center gap-1.5">
              <Search className="w-3 h-3" />
              &ldquo;{searchTerm}&rdquo;
              <button onClick={() => setSearchTerm('')} className="hover:text-blue-900">
                <X className="w-3.5 h-3.5" />
              </button>
            </span>
          )}
          
          {dateRange !== 'All time' && (
            <span className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm flex items-center gap-1.5">
              <Calendar className="w-3 h-3" />
              {dateRange}
              <button onClick={() => setDateRange('All time')} className="hover:text-blue-900">
                <X className="w-3.5 h-3.5" />
              </button>
            </span>
          )}
          
          {selectedTags.map(tag => (
            <span key={tag} className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm flex items-center gap-1.5">
              <Tag className="w-3 h-3" />
              {tag}
              <button onClick={() => handleTagToggle(tag)} className="hover:text-blue-900">
                <X className="w-3.5 h-3.5" />
              </button>
            </span>
          ))}
          
          <button 
            onClick={clearFilters}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            Clear all
          </button>
        </motion.div>
      )}
      
      {/* Filter content */}
      <motion.div 
        initial={false}
        animate={{ 
          height: isFilterOpen ? 'auto' : 0,
          opacity: isFilterOpen ? 1 : 0,
          marginBottom: isFilterOpen ? '2rem' : 0
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
          <div className="flex flex-col gap-6">
            {/* Date filter */}
            <div>
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Calendar className="w-4 h-4 text-blue-500" />
                Date Range
              </h3>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {DATE_RANGES.map((range) => (
                  <button
                    key={range.label}
                    onClick={() => setDateRange(range.label)}
                    className={`px-3 py-1.5 rounded-full text-sm transition-all duration-200 ${
                      dateRange === range.label 
                        ? 'bg-blue-100 text-blue-700 font-medium shadow-sm' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {range.label}
                  </button>
                ))}
              </div>
              
              {dateRange === 'Up to 360 days' && (
                <DateRangeSlider 
                  value={customDateRange} 
                  onChange={setCustomDateRange} 
                  min={7} 
                  max={360} 
                />
              )}
            </div>
            
            {/* Tags filter */}
            <div>
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Tag className="w-4 h-4 text-blue-500" />
                Filter by Tags
                {selectedTags.length > 0 && (
                  <span className="text-xs text-blue-700 bg-blue-50 px-2 py-0.5 rounded-full">
                    {selectedTags.length} selected
                  </span>
                )}
              </h3>
              
              <div className="flex flex-wrap gap-2 max-h-36 overflow-y-auto pb-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {uniqueTags.map((tag) => (
                  <motion.button
                    key={tag}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleTagToggle(tag)}
                    className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-1.5 ${
                      selectedTags.includes(tag) 
                        ? 'bg-blue-100 text-blue-700 border border-blue-200 shadow-sm' 
                        : 'bg-gray-100 text-gray-600 border border-transparent hover:bg-gray-200'
                    }`}
                  >
                    {tag}
                    {selectedTags.includes(tag) && (
                      <X className="w-3 h-3" />
                    )}
                  </motion.button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default function BlogSection() {
  const [showAllBlogs, setShowAllBlogs] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState("All time");
  const [customDateRange, setCustomDateRange] = useState(360);
  const [selectedTags, setSelectedTags] = useState([]);

  // Extract all unique tags from blogs
  const allTags = useMemo(() => {
    const tagsSet = new Set();
    blogs.forEach(blog => {
      if (blog.tags && Array.isArray(blog.tags)) {
        blog.tags.forEach(tag => tagsSet.add(tag));
      }
    });
    return Array.from(tagsSet).sort();
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= MOBILE_BREAKPOINT;
      setIsMobile(mobile);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Parse blog dates and filter by date range
  const filteredBlogs = useMemo(() => {
    return blogs.filter((blog) => {
      // Search term filter
      const matchesSearch = searchTerm === "" || 
        blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        blog.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        blog.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      // Date filter
      let matchesDate = true;
      if (dateRange !== 'All time') {
        try {
          const blogDate = parse(blog.date, 'MMMM d, yyyy', new Date());
          const today = new Date();
          
          if (dateRange === 'Up to 360 days') {
            const cutoffDate = subDays(today, customDateRange);
            matchesDate = isAfter(blogDate, cutoffDate) || isEqual(blogDate, cutoffDate);
          } else {
            const days = DATE_RANGES.find(r => r.label === dateRange)?.days || null;
            if (days) {
              const cutoffDate = subDays(today, days);
              matchesDate = isAfter(blogDate, cutoffDate) || isEqual(blogDate, cutoffDate);
            }
          }
        } catch (e) {
          console.error("Error parsing date:", blog.date);
          matchesDate = true;
        }
      }
      
      // Tags filter
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.every(tag => blog.tags?.includes(tag));
      
      return matchesSearch && matchesDate && matchesTags;
    });
  }, [searchTerm, dateRange, customDateRange, selectedTags]);
  
  const featuredBlogs = filteredBlogs.filter(blog => blog.featured);
  const regularBlogs = filteredBlogs.filter(blog => !blog.featured);
  
  const visibleRegularBlogs = showAllBlogs ? regularBlogs : regularBlogs.slice(0, VISIBLE_BLOGS_COUNT);

  // Check if any filter is active
  const isFilterActive = searchTerm !== "" || dateRange !== "All time" || selectedTags.length > 0;

  // Generate breadcrumbs for blog listing page
  const blogListingBreadcrumbs = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Blog",
      url: "/Blogs",
      current: true,
      iconKey: "blogs",
      description: "Cybersecurity insights and penetration testing guidance"
    }
  ];

  return (
    <div className="bg-gray-50 py-16 sm:py-24 lg:py-26 px-4 sm:px-6 lg:px-8 relative">
      {/* Breadcrumb Navigation - positioned absolutely at the top */}
      <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14">
        <div className="max-w-7xl px-2 sm:px-2 md:px-16">
          <BreadcrumbNavigation
            items={blogListingBreadcrumbs}
            className="text-blue-600"
          />
        </div>
      </div>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="max-w-7xl mx-auto">
              {/* Header Section */}
              <div className="text-center mb-12 sm:mb-16">
              <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-500 px-3 sm:px-4 py-2 rounded-full text-sm font-medium mb-4 sm:mb-6">
                <Tag className="w-4 h-4" />
                Industry Insights & Expertise
              </div>
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 px-4">
                Security Insights Hub
              </h1>
              <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed px-4">
                Curated content for the security professional: We cover the latest on frameworks, threats, and cybersecurity trends to keep your organization ahead of emerging risks.
              </p>
            </div>
            
            {/* Filter Panel */}
            <FilterPanel 
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              dateRange={dateRange}
              setDateRange={setDateRange}
              selectedTags={selectedTags}
              setSelectedTags={setSelectedTags}
              customDateRange={customDateRange}
              setCustomDateRange={setCustomDateRange}
              isFilterOpen={isFilterOpen}
              setIsFilterOpen={setIsFilterOpen}
              isMobile={isMobile}
            />
            
            {/* No results message */}
            {filteredBlogs.length === 0 && (
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-16 bg-white rounded-xl shadow-sm border border-gray-100"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-50 mb-4">
                  <Search className="w-8 h-8 text-blue-400" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">No matching articles found</h3>
                <p className="text-gray-600 mb-6">Try adjusting your search or filter criteria</p>
                <button 
                  onClick={() => {
                    setSearchTerm('');
                    setDateRange('All time');
                    setSelectedTags([]);
                  }}
                  className="px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Reset all filters
                </button>
              </motion.div>
            )}
 
            {!isFilterActive && featuredBlogs.length > 0 && (
              <div className="mb-12 sm:mb-16">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-6 sm:mb-8 flex items-center gap-2 px-4 sm:px-0">
                  <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  Featured Articles
                </h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 sm:gap-8 px-4 sm:px-0">
                  {featuredBlogs.map((blog, index) => (
                    <FeaturedBlogCard key={index} blog={blog} />
                  ))}
                </div>
              </div>
            )}

            {filteredBlogs.length > 0 && (
              <div className="mb-8 sm:mb-12">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-6 sm:mb-8 flex items-center gap-2 px-4 sm:px-0">
                  <span className="w-2 h-2 bg-gray-600 rounded-full"></span>
                  {isFilterActive ? 'Filtered Articles' : 'Latest Articles'}
                  <span className="text-sm font-normal text-gray-500">
                    ({isFilterActive ? filteredBlogs.length : regularBlogs.length} {(isFilterActive ? filteredBlogs.length : regularBlogs.length) === 1 ? 'article' : 'articles'})
                  </span>
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 px-4 sm:px-0">
                  {isFilterActive 
                    ? (showAllBlogs ? filteredBlogs : filteredBlogs.slice(0, VISIBLE_BLOGS_COUNT)).map((blog, index) => (
                        <BlogCard key={index} blog={blog} />
                      ))
                    : visibleRegularBlogs.map((blog, index) => (
                        <BlogCard key={index} blog={blog} />
                      ))
                  }
                </div>
              </div>
            )}

            {/* Load More Button */}
            {(isFilterActive ? filteredBlogs.length > VISIBLE_BLOGS_COUNT : regularBlogs.length > VISIBLE_BLOGS_COUNT) && (
              <div className="flex justify-center px-4 sm:px-0">
                <Button
                  variant="primary"
                  onClick={() => setShowAllBlogs((prev) => !prev)}
                  rightIcon={<ChevronRight className="w-4 h-4" />}
                >
                  {showAllBlogs ? "Show Less Articles" : "Load More Articles"}
                </Button>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}

